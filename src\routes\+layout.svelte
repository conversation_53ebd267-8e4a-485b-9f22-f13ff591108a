<script lang="ts">
  import '../app.css'
  import Navbar from '$lib/components/Navbar.svelte'
  import { page, navigating } from '$app/stores'
  import { onMount } from 'svelte'
  import { browser } from '$app/environment'

  $: session = $page.data.session

  let previousPath = ''

  // 页面导航时滚动到顶部
  $: if (browser && !$navigating && $page.url.pathname !== previousPath) {
    previousPath = $page.url.pathname
    // 使用 requestAnimationFrame 确保在下一帧执行
    requestAnimationFrame(() => {
      window.scrollTo({ top: 0, left: 0, behavior: 'instant' })
    })
  }

  // 组件挂载时初始化
  onMount(() => {
    previousPath = $page.url.pathname
    // 初始加载时滚动到顶部
    window.scrollTo({ top: 0, left: 0, behavior: 'instant' })
  })
</script>

<svelte:head>
  <title>Excel学习平台 - 游戏闯关模式</title>
  <meta name="description" content="通过有趣的游戏闯关方式学习Excel，从基础操作到高级技巧" />

</svelte:head>

<div class="antialiased">
  <Navbar {session} />
  <slot />
</div>
