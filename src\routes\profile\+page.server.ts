import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'
import { getSession } from '$lib/session'

export const load: PageServerLoad = async ({ cookies, fetch }) => {
  const session = await getSession(cookies)

  // 检查是否已登录
  if (!session?.user) {
    throw redirect(302, '/login')
  }
  
  try {
    // 获取邀请码信息
    const response = await fetch('/api/invite-codes')
    
    let inviteCodes = {
      codes: [],
      total: 0,
      used: 0,
      available: 0
    }
    
    if (response.ok) {
      inviteCodes = await response.json()
    }
    
    return {
      session,
      inviteCodes
    }
  } catch (error) {
    console.error('Error loading profile data:', error)
    
    return {
      session,
      inviteCodes: {
        codes: [],
        total: 0,
        used: 0,
        available: 0
      }
    }
  }
}
