import { error, redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'
import { getSession } from '$lib/session'

export const load: PageServerLoad = async ({ params, cookies, fetch }) => {
  const session = await getSession(cookies)

  // 检查是否已登录
  if (!session?.user) {
    throw redirect(302, '/login')
  }
  
  try {
    // 获取任务详情
    const response = await fetch(`/api/tasks/${params.id}`)
    
    if (!response.ok) {
      if (response.status === 404) {
        throw error(404, '任务不存在')
      } else if (response.status === 403) {
        const result = await response.json()
        throw error(403, result.error || '您没有权限访问此任务')
      } else {
        throw error(500, '获取任务信息失败')
      }
    }
    
    const { task, level } = await response.json()
    
    return {
      session,
      task,
      level
    }
  } catch (err) {
    if (err instanceof Error && err.message.includes('redirect')) {
      throw err
    }
    
    console.error('Error loading task data:', err)
    throw error(500, '服务器错误')
  }
}
