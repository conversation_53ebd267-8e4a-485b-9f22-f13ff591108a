import type { RequestHandler } from './$types'
import { json, redirect } from '@sveltejs/kit'
import { destroySession } from '$lib/session'

export const POST: RequestHandler = async ({ cookies }) => {
  // 销毁session
  destroySession(cookies)
  
  // 重定向到首页
  throw redirect(302, '/')
}

export const GET: RequestHandler = async ({ cookies }) => {
  // 销毁session
  destroySession(cookies)
  
  // 重定向到首页
  throw redirect(302, '/')
}
