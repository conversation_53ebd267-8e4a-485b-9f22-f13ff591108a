// 浏览器测试脚本
import { chromium } from '@playwright/test'

async function browserTest() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000 // 慢速执行，便于观察
  })
  
  const page = await browser.newPage()
  
  try {
    console.log('🌐 开始浏览器测试...')
    
    // 1. 访问首页
    await page.goto('http://localhost:5173')
    console.log('✅ 访问首页')
    await page.waitForTimeout(2000)
    
    // 2. 点击登录
    await page.click('a[href="/login"]')
    await page.waitForURL('**/login')
    console.log('✅ 进入登录页面')
    
    // 3. 填写登录信息
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    console.log('✅ 填写登录信息')
    
    // 4. 点击登录按钮
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功，进入dashboard')
    
    // 5. 检查导航栏经验值（初始应该是0）
    await page.waitForSelector('nav')
    const navText = await page.textContent('nav')
    console.log('📊 导航栏内容:', navText)
    
    // 查找经验值显示
    const scoreElements = await page.locator('text=/\\d+\\s*经验值|\\d+\\s*分|经验.*\\d+/').all()
    for (let i = 0; i < scoreElements.length; i++) {
      const text = await scoreElements[i].textContent()
      console.log(`📊 找到经验值显示 ${i+1}:`, text)
    }
    
    // 6. 检查dashboard经验值显示
    const dashboardContent = await page.textContent('main')
    console.log('📈 Dashboard内容包含经验值:', dashboardContent.includes('经验值') || dashboardContent.includes('积分'))
    
    // 7. 点击"基础操作"进入关卡列表
    await page.click('text=基础操作')
    await page.waitForLoadState('networkidle')
    console.log('✅ 进入基础操作关卡列表')
    
    // 8. 查找"数据输入"任务
    const dataInputTask = page.locator('text=数据输入').first()
    if (await dataInputTask.isVisible()) {
      console.log('✅ 找到"数据输入"任务')
      
      // 检查按钮文本（应该是"开始挑战"）
      const buttonText = await page.locator('text=数据输入').locator('..').locator('..').locator('a').last().textContent()
      console.log('🎯 按钮文本:', buttonText)
      
      // 9. 点击"数据输入"任务
      await page.locator('text=数据输入').locator('..').locator('..').locator('a').last().click()
      await page.waitForLoadState('networkidle')
      console.log('✅ 进入"数据输入"任务页面')
      
      // 10. 查找并点击"完成任务"按钮
      const completeButton = page.locator('button:has-text("完成任务"), button:has-text("提交答案"), button:has-text("完成")')
      if (await completeButton.isVisible()) {
        await completeButton.click()
        console.log('✅ 点击完成任务按钮')
        
        // 等待可能的成功提示或页面跳转
        await page.waitForTimeout(3000)
        
        // 11. 检查是否有成功提示
        const pageContent = await page.textContent('body')
        if (pageContent.includes('恭喜') || pageContent.includes('完成') || pageContent.includes('成功')) {
          console.log('✅ 看到成功提示')
        }
        
        // 12. 返回dashboard检查经验值更新
        await page.goto('http://localhost:5173/dashboard')
        await page.waitForLoadState('networkidle')
        console.log('✅ 返回dashboard')
        
        // 13. 再次检查经验值
        const updatedNavText = await page.textContent('nav')
        console.log('📊 更新后导航栏内容:', updatedNavText)
        
        const updatedScoreElements = await page.locator('text=/\\d+\\s*经验值|\\d+\\s*分|经验.*\\d+/').all()
        for (let i = 0; i < updatedScoreElements.length; i++) {
          const text = await updatedScoreElements[i].textContent()
          console.log(`📊 更新后经验值显示 ${i+1}:`, text)
        }
        
        // 14. 再次进入"数据输入"任务检查按钮状态
        await page.click('text=基础操作')
        await page.waitForLoadState('networkidle')
        
        const updatedButtonText = await page.locator('text=数据输入').locator('..').locator('..').locator('a').last().textContent()
        console.log('🎯 更新后按钮文本:', updatedButtonText)
        
        if (updatedButtonText && updatedButtonText.includes('再次挑战')) {
          console.log('✅ 按钮正确更新为"再次挑战"')
        } else {
          console.log('❌ 按钮未正确更新')
        }
      } else {
        console.log('❌ 未找到完成任务按钮')
      }
    } else {
      console.log('❌ 未找到"数据输入"任务')
    }
    
    console.log('🎉 浏览器测试完成！')
    
    // 保持浏览器打开30秒供手动检查
    console.log('⏰ 浏览器将保持打开30秒供手动检查...')
    await page.waitForTimeout(30000)
    
  } catch (error) {
    console.error('❌ 浏览器测试失败:', error)
    
    // 截图保存错误状态
    await page.screenshot({ path: 'error-screenshot.png' })
    console.log('📸 已保存错误截图: error-screenshot.png')
  } finally {
    await browser.close()
  }
}

browserTest()
