import type { Actions, PageServerLoad } from './$types'
import { redirect, fail } from '@sveltejs/kit'
import { prisma } from '$lib/db'
import bcrypt from 'bcryptjs'
import { createSession, getSession } from '$lib/session'

export const load: PageServerLoad = async ({ cookies }) => {
  // 检查是否已经登录
  const session = await getSession(cookies)

  if (session) {
    // 如果已经登录，重定向到dashboard
    throw redirect(302, '/dashboard')
  }

  return {}
}

export const actions: Actions = {
  default: async ({ request, cookies }) => {
    console.log('Login action called')
    const formData = await request.formData()
    const email = formData.get('email') as string
    const password = formData.get('password') as string

    console.log('Login attempt:', { email, password: password ? '***' : 'empty' })

    if (!email || !password) {
      console.log('Missing email or password')
      return fail(400, {
        error: '请填写邮箱和密码',
        email
      })
    }

    try {
      // 验证用户凭据
      const user = await prisma.user.findUnique({
        where: { email }
      })

      if (!user) {
        console.log('User not found:', email)
        return fail(400, {
          error: '邮箱或密码错误',
          email
        })
      }

      if (!user.emailVerified) {
        console.log('Email not verified:', email)
        return fail(400, {
          error: '请先验证您的邮箱地址',
          email
        })
      }

      const isPasswordValid = await bcrypt.compare(password, user.password)

      if (!isPasswordValid) {
        console.log('Invalid password for:', email)
        return fail(400, {
          error: '邮箱或密码错误',
          email
        })
      }

      // 登录成功，创建session
      console.log('Login successful')

      await createSession({
        id: user.id,
        email: user.email,
        username: user.username,
        score: user.score,
        userType: user.userType
      }, cookies)

    } catch (error) {
      console.log('Login error:', error)
      return fail(400, {
        error: '登录失败，请重试',
        email
      })
    }

    // 返回成功响应，让客户端处理重定向
    return {
      success: true,
      redirectTo: '/dashboard'
    }
  }
}


