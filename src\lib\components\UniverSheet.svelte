<script lang="ts">
  import { onMount, onDestroy } from 'svelte'
  import type { UniverAPI, UniverInstance, WorkbookData } from '../types/univer'
  import { log } from '../logger'

  // 导入核心模块
  import { LocaleType, mergeLocales, Univer, UniverInstanceType } from "@univerjs/core";
  import { FUniver } from "@univerjs/core/facade";
  import { defaultTheme } from "@univerjs/design";

  // 核心插件 - 立即加载
  import { UniverFormulaEnginePlugin } from "@univerjs/engine-formula";
  import { UniverRenderEnginePlugin } from "@univerjs/engine-render";
  import { UniverUIPlugin } from "@univerjs/ui";
  import { UniverDocsPlugin } from "@univerjs/docs";
  import { UniverDocsUIPlugin } from "@univerjs/docs-ui";
  import { UniverSheetsPlugin } from "@univerjs/sheets";
  import { UniverSheetsUIPlugin } from "@univerjs/sheets-ui";

  // 核心语言包 - 立即加载
  import DesignZhCN from '@univerjs/design/locale/zh-CN';
  import UIZhCN from '@univerjs/ui/locale/zh-CN';
  import DocsUIZhCN from '@univerjs/docs-ui/locale/zh-CN';
  import SheetsZhCN from '@univerjs/sheets/locale/zh-CN';
  import SheetsUIZhCN from '@univerjs/sheets-ui/locale/zh-CN';

  // 懒加载插件的语言包 - 预先加载以确保菜单显示正确
  import SheetsFormulaUIZhCN from '@univerjs/sheets-formula-ui/locale/zh-CN';
  import SheetsNumfmtUIZhCN from '@univerjs/sheets-numfmt-ui/locale/zh-CN';
  import SheetsDataValidationZhCN from '@univerjs/sheets-data-validation-ui/locale/zh-CN';
  import SheetsFilterUIZhCN from '@univerjs/sheets-filter-ui/locale/zh-CN';
  import SheetsSortUIZhCN from '@univerjs/sheets-sort-ui/locale/zh-CN';
  import SheetsConditionalFormattingUIZhCN from '@univerjs/sheets-conditional-formatting-ui/locale/zh-CN';
  import SheetsTableUIZhCN from '@univerjs/sheets-table-ui/locale/zh-CN';
  import SheetsPivotTableUIZhCN from '@univerjs-pro/sheets-pivot-ui/locale/zh-CN';
  import SheetsPivotTableZhCN from '@univerjs-pro/sheets-pivot/locale/zh-CN';
  import SheetsChartUIZhCN from '@univerjs-pro/sheets-chart-ui/locale/zh-CN';
  import SheetsChartZhCN from '@univerjs-pro/sheets-chart/locale/zh-CN';

  // 核心 CSS - 立即加载
  import "@univerjs/design/lib/index.css";
  import "@univerjs/ui/lib/index.css";
  import "@univerjs/docs-ui/lib/index.css";
  import "@univerjs/sheets-ui/lib/index.css";

  // 懒加载插件的CSS - 保留立即加载以避免样式闪烁
  import "@univerjs/sheets-formula-ui/lib/index.css";
  import "@univerjs/sheets-numfmt-ui/lib/index.css";
  import '@univerjs/sheets-data-validation-ui/lib/index.css';
  import '@univerjs/sheets-filter-ui/lib/index.css';
  import '@univerjs/sheets-sort-ui/lib/index.css';
  import '@univerjs/sheets-conditional-formatting-ui/lib/index.css';
  import '@univerjs/sheets-table-ui/lib/index.css';

  // 高级插件的CSS
  import '@univerjs-pro/sheets-chart-ui/lib/index.css';
  import '@univerjs-pro/sheets-pivot-ui/lib/index.css';

  // 核心 Facade API - 只导入立即需要的
  import '@univerjs/ui/facade';
  import '@univerjs/docs-ui/facade';
  import '@univerjs/sheets/facade';
  import '@univerjs/sheets-ui/facade';

  export let onReady: ((instance: UniverInstance, api: UniverAPI) => void) | undefined = undefined
  export let initialData: Record<string, unknown> | undefined = undefined

  let containerRef: HTMLDivElement
  let univerAPI: UniverAPI | null = null
  let univerInstance: UniverInstance | null = null
  let isMounted = true

  onMount(async () => {
    if (univerInstance) {
      return;
    }

    isMounted = true;
    let advancedPluginTimer: NodeJS.Timeout | null = null;

    try {
      log.debug('开始初始化 Univer...');

      const univer = new Univer({
        theme: defaultTheme,
        locale: LocaleType.ZH_CN,
        locales: {
          [LocaleType.ZH_CN]: mergeLocales(
            DesignZhCN,
            UIZhCN,
            DocsUIZhCN,
            SheetsZhCN,
            SheetsUIZhCN,
            // 预加载懒加载插件的语言包，确保菜单显示正确
            SheetsFormulaUIZhCN,
            SheetsNumfmtUIZhCN,
            SheetsDataValidationZhCN,
            SheetsFilterUIZhCN,
            SheetsSortUIZhCN,
            SheetsConditionalFormattingUIZhCN,
            SheetsTableUIZhCN,
            SheetsPivotTableZhCN,
            SheetsPivotTableUIZhCN,
            SheetsChartZhCN,
            SheetsChartUIZhCN,
          ),
        },
      });

      // 核心插件注册
      univer.registerPlugin(UniverRenderEnginePlugin);
      univer.registerPlugin(UniverFormulaEnginePlugin);

      univer.registerPlugin(UniverUIPlugin, {
        container: containerRef,
        // 隐藏"插入"菜单中的图片菜单项，因为有bug不显示title
        menu: {
          'sheet.menu.image': {
            hidden: true,
            disabled: true
          }
        },
      });

      univer.registerPlugin(UniverDocsPlugin);
      univer.registerPlugin(UniverDocsUIPlugin);
      univer.registerPlugin(UniverSheetsPlugin);
      univer.registerPlugin(UniverSheetsUIPlugin);

      log.debug('核心插件加载完成');

      // 准备工作表数据，包含初始数据
      const workbookData: WorkbookData = {
        id: 'workbook-01',
        locale: LocaleType.ZH_CN,
        name: 'UniverSheet',
        sheetOrder: ['sheet-01'],
        sheets: {
          'sheet-01': {
            id: 'sheet-01',
            name: '工作表1',
            cellData: {},
          },
        },
      };

      // 如果有初始数据，直接添加到工作表数据中
      if (initialData && Object.keys(initialData).length > 0) {
        Object.entries(initialData).forEach(([cell, value]) => {
          // 解析单元格地址，如 "B1" -> {row: 0, col: 1}
          const match = cell.match(/^([A-Z]+)(\d+)$/);
          if (match) {
            const colStr = match[1];
            const rowNum = parseInt(match[2]) - 1; // 转换为0基索引
            
            // 将列字母转换为数字
            let colNum = 0;
            for (let i = 0; i < colStr.length; i++) {
              colNum = colNum * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
            }
            colNum -= 1; // 转换为0基索引
            
            if (!workbookData.sheets['sheet-01'].cellData[rowNum]) {
              workbookData.sheets['sheet-01'].cellData[rowNum] = {};
            }
            
            // 处理数据类型，确保数字被正确识别和格式化
            let cellValue = value;
            let cellType: number | undefined;
            
            // 如果是数字类型，确保正确设置
            if (typeof value === 'number') {
              cellValue = value;
              cellType = 2; // 数字类型
            }
            // 如果是字符串且看起来像数字，转换为数字
            else if (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '') {
              cellValue = Number(value);
              cellType = 2; // 数字类型
            }
            
            const cellData: any = {
              v: cellValue,
            };
            
            // 如果是数字类型，设置类型标识
            if (cellType !== undefined) {
              cellData.t = cellType;
            }
            
            workbookData.sheets['sheet-01'].cellData[rowNum][colNum] = cellData;
          }
        });
        log.debug('初始数据已集成到工作表:', initialData);
      }

      // 创建工作簿
      univer.createUnit(UniverInstanceType.UNIVER_SHEET, workbookData);

      // 获取 Facade API
      const univerAPI = FUniver.newAPI(univer);
      
      // 保存引用
      univerInstance = univer;
      
      log.debug('Univer实例已准备就绪');

      // 通知父组件Univer实例已准备就绪
      if (onReady) {
        onReady(univer, univerAPI as UniverAPI);
      }

      // 懒加载基础功能插件
      const loadBasicPlugins = async () => {
        if (isMounted && univerInstance) {
          try {
            log.debug('开始加载懒加载插件...');

            // 并行加载Facade和插件以提升性能
            const [, lazy] = await Promise.all([
              // 动态加载基础功能Facade
              Promise.all([
                import('@univerjs/engine-formula/facade'),
                import('@univerjs/sheets-formula/facade'),
                import('@univerjs/sheets-numfmt/facade'),
                import('@univerjs/sheets-data-validation/facade'),
                import('@univerjs/sheets-filter/facade'),
                import('@univerjs/sheets-sort/facade'),
                import('@univerjs/sheets-conditional-formatting/facade'),
                import('@univerjs/sheets-table/facade'),
              ]).catch(() => log.warn('基础功能Facade加载失败')),

              // 加载插件
              import('./lazy')
            ]);

            // 注册插件
            if (univerInstance) {
              const plugins = lazy.default();
              plugins.forEach((p) => {
                if (univerInstance && univerInstance.registerPlugin) {
                  univerInstance.registerPlugin(p[0], p[1]);
                }
              });
            }

            log.debug('懒加载插件加载完成');
          } catch (error) {
            log.warn('基础功能插件懒加载失败:', error);
          }
        }
      };

      // 第二阶段：高级功能插件（延迟更长时间）
      const loadAdvancedPlugins = () => {
        if (isMounted && univerInstance) {
          try {
            import('./lazy').then((lazy) => {
              try {
                if (univerInstance) {
                  const plugins = lazy.getVeryLazyPlugins();
                  plugins.forEach((p) => {
                    if (univerInstance && univerInstance.registerPlugin) {
                      univerInstance.registerPlugin(p[0], p[1]);
                    }
                  });
                }
                log.debug('高级功能插件懒加载完成');
              } catch (error) {
                log.warn('高级功能插件懒加载失败:', error);
              }
            }).catch((error) => {
              log.warn('高级懒加载模块导入失败:', error);
            });
          } catch (error) {
            log.warn('高级功能插件懒加载失败:', error);
          }
        }
      };

      // 使用requestIdleCallback优化加载时机
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        window.requestIdleCallback(loadBasicPlugins, { timeout: 100 });
      } else {
        setTimeout(loadBasicPlugins, 16); // 一帧的时间
      }

      // 延迟加载高级插件
      setTimeout(loadAdvancedPlugins, 500);

    } catch (error) {
      log.error('Univer初始化失败:', error);
    }
  });

  onDestroy(() => {
    // 标记组件已卸载
    isMounted = false;

    // 清理资源
    try {
      if (univerAPI) {
        univerAPI.dispose?.();
        univerAPI = null;
      }
      if (univerInstance) {
        univerInstance.dispose?.();
        univerInstance = null;
      }
    } catch (error) {
      if (isMounted) {
        log.error('Univer清理失败:', error);
      }
    }
  });
</script>

<div
  bind:this={containerRef}
  class="univer-container"
  style="height: 100%; width: 100%;"
></div>
