import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function analyzeProblemTasks() {
  try {
    // 正确操作无法完成的任务
    const correctOperationFailTasks = [
      '字体更改', '字体颜色设置', '单元格对齐方式', '边框设置', 
      '简单排序', '复杂排序', '简单数据验证', '复杂数据验证', 
      '简单条件格式', '单元格合并', '自动换行和强制换行', '快速填充公式'
    ];
    
    // 错误操作能完成的任务
    const wrongOperationPassTasks = [
      '简单筛选', '复杂筛选', '复杂条件格式', '数据透视表'
    ];
    
    console.log('='.repeat(80));
    console.log('📊 正确操作无法完成的任务分析');
    console.log('='.repeat(80));
    
    for (const taskName of correctOperationFailTasks) {
      const task = await prisma.task.findFirst({
        where: { name: taskName }
      });
      
      if (task) {
        console.log(`\n任务: ${task.name}`);
        console.log(`ID: ${task.id}`);
        console.log(`验证规则:`);
        
        if (task.validation) {
          try {
            const validationRule = JSON.parse(task.validation);
            console.log(JSON.stringify(validationRule, null, 2));
          } catch (e) {
            console.log('验证规则解析失败:', task.validation);
          }
        } else {
          console.log('❌ 没有验证规则');
        }
        
        console.log('-'.repeat(60));
      } else {
        console.log(`❌ 未找到任务: ${taskName}`);
      }
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 错误操作能完成的任务分析');
    console.log('='.repeat(80));
    
    for (const taskName of wrongOperationPassTasks) {
      const task = await prisma.task.findFirst({
        where: { name: taskName }
      });
      
      if (task) {
        console.log(`\n任务: ${task.name}`);
        console.log(`ID: ${task.id}`);
        console.log(`验证规则:`);
        
        if (task.validation) {
          try {
            const validationRule = JSON.parse(task.validation);
            console.log(JSON.stringify(validationRule, null, 2));
          } catch (e) {
            console.log('验证规则解析失败:', task.validation);
          }
        } else {
          console.log('❌ 没有验证规则');
        }
        
        console.log('-'.repeat(60));
      } else {
        console.log(`❌ 未找到任务: ${taskName}`);
      }
    }
    
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

analyzeProblemTasks();
