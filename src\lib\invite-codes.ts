import { prisma } from './db'
import { log } from './logger'

/**
 * 生成随机邀请码
 */
function generateInviteCode(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 验证邀请码
 */
export async function validateInviteCode(code: string): Promise<{
  isValid: boolean
  type: 'beta' | 'friend'
  id: string
} | null> {
  try {
    // 检查是否是内测邀请码（8位）
    if (code.length === 8) {
      const betaCode = await prisma.betaInviteCode.findUnique({
        where: { code }
      })
      
      if (betaCode) {
        return {
          isValid: !betaCode.isUsed,
          type: 'beta',
          id: betaCode.id
        }
      }
    }
    
    // 检查是否是好友邀请码（6位）
    if (code.length === 6) {
      const friendCode = await prisma.friendInviteCode.findUnique({
        where: { code }
      })
      
      if (friendCode) {
        return {
          isValid: !friendCode.isUsed,
          type: 'friend',
          id: friendCode.id
        }
      }
    }
    
    return null
  } catch (error) {
    log.error('验证邀请码失败:', error)
    return null
  }
}

/**
 * 使用邀请码
 */
export async function useInviteCode(code: string, userId: string): Promise<boolean> {
  try {
    const validation = await validateInviteCode(code)
    if (!validation || !validation.isValid) {
      return false
    }
    
    if (validation.type === 'beta') {
      await prisma.betaInviteCode.update({
        where: { id: validation.id },
        data: {
          isUsed: true,
          usedBy: userId,
          usedAt: new Date()
        }
      })
    } else if (validation.type === 'friend') {
      await prisma.friendInviteCode.update({
        where: { id: validation.id },
        data: {
          isUsed: true,
          usedBy: userId,
          usedAt: new Date()
        }
      })
    }
    
    return true
  } catch (error) {
    log.error('使用邀请码失败:', error)
    return false
  }
}

/**
 * 为用户分配好友邀请码
 */
export async function allocateFriendCodes(userId: string, count: number): Promise<string[]> {
  try {
    const codes: string[] = []
    
    for (let i = 0; i < count; i++) {
      let code: string
      let isUnique = false
      
      // 确保生成的邀请码是唯一的
      while (!isUnique) {
        code = generateInviteCode(6)
        
        const existing = await prisma.friendInviteCode.findUnique({
          where: { code }
        })
        
        if (!existing) {
          isUnique = true
          codes.push(code)
          
          await prisma.friendInviteCode.create({
            data: {
              code,
              ownerId: userId
            }
          })
        }
      }
    }
    
    return codes
  } catch (error) {
    log.error('分配好友邀请码失败:', error)
    return []
  }
}

/**
 * 获取用户的好友邀请码
 */
export async function getUserFriendCodes(userId: string) {
  try {
    const codes = await prisma.friendInviteCode.findMany({
      where: { ownerId: userId },
      orderBy: { createdAt: 'asc' },
      select: {
        id: true,
        code: true,
        isUsed: true,
        usedBy: true,
        usedAt: true,
        createdAt: true
      }
    })
    
    return {
      codes,
      total: codes.length,
      used: codes.filter(code => code.isUsed).length,
      available: codes.filter(code => !code.isUsed).length
    }
  } catch (error) {
    log.error('获取用户好友邀请码失败:', error)
    return {
      codes: [],
      total: 0,
      used: 0,
      available: 0
    }
  }
}

/**
 * 检查用户是否有高级访问权限
 */
export function hasAdvancedAccess(userType: string, score: number, levelName: string): boolean {
  // 内测用户有所有权限
  if (userType === 'beta') {
    return true
  }

  // 好友用户的权限检查
  if (userType === 'friend') {
    if (levelName === '实用技巧') {
      return score >= 600
    }
    return true
  }

  // 普通用户的权限检查
  if (userType === 'normal') {
    // 基础关卡对所有用户开放
    if (levelName === '初识表格' || levelName === '基本公式' || levelName === '常用操作' || levelName === '进阶公式') {
      return true
    }
    if (levelName === '进阶操作') {
      return score >= 500
    }
    if (levelName === '实用技巧') {
      return score >= 600
    }
  }

  return false
}

/**
 * 获取关卡访问信息
 */
export function getLevelAccessInfo(levelName: string, userType: string, score: number) {
  const hasAccess = hasAdvancedAccess(userType, score, levelName)

  let buttonText = '开始学习'
  let isLocked = false
  let requiredScore = 0

  // 检查是否是专属区域
  const isExclusiveArea = levelName === '进阶操作' || levelName === '实用技巧'

  if (!hasAccess) {
    isLocked = true

    if (userType === 'normal') {
      if (levelName === '进阶操作') {
        buttonText = '需要500经验值'
        requiredScore = 500
      } else if (levelName === '实用技巧') {
        buttonText = '需要600经验值'
        requiredScore = 600
      }
    } else if (userType === 'friend' && levelName === '实用技巧') {
      buttonText = '需要600经验值'
      requiredScore = 600
    } else {
      buttonText = '需要邀请码'
    }
  } else if (hasAccess && isExclusiveArea) {
    // 对于有权限访问专属区域的用户，显示特殊按钮文本
    if (userType === 'beta' || userType === 'friend') {
      buttonText = '专属区域，已对您开放'
    } else if (userType === 'normal') {
      // 普通用户达到经验值要求后也显示专属区域文本
      buttonText = '专属区域，已对您开放'
    }
  }

  return {
    hasAccess,
    isLocked,
    buttonText,
    requiredScore
  }
}
