import type { Cookies } from '@sveltejs/kit'
import jwt from 'jsonwebtoken'
import { prisma } from './db'

const JWT_SECRET = process.env.AUTH_SECRET || 'your-secret-key'

export interface SessionUser {
  id: string
  email: string
  username: string
  score: number
  userType: string
}

export interface Session {
  user: SessionUser
}

export async function createSession(user: SessionUser, cookies: Cookies): Promise<void> {
  const token = jwt.sign(
    {
      userId: user.id,
      email: user.email,
      username: user.username,
      score: user.score,
      userType: user.userType
    },
    JWT_SECRET,
    { expiresIn: '7d' }
  )

  cookies.set('session', token, {
    path: '/',
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60 * 24 * 7 // 7 days
  })
}

export async function getSession(cookies: Cookies): Promise<Session | null> {
  const token = cookies.get('session')
  
  if (!token) {
    return null
  }

  try {
    const payload = jwt.verify(token, JWT_SECRET) as any
    
    // 验证用户是否仍然存在，并获取最新的用户数据
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: { id: true, email: true, username: true, score: true, userType: true }
    })

    if (!user) {
      return null
    }

    return {
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        score: user.score,
        userType: user.userType
      }
    }
  } catch (error) {
    console.error('Session verification failed:', error)
    return null
  }
}

export function destroySession(cookies: Cookies): void {
  cookies.delete('session', { path: '/' })
}
