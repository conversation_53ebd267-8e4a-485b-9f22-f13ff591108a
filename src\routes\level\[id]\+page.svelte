<script lang="ts">
  import { onMount } from 'svelte'
  import { page } from '$app/stores'
  import { navbarStore } from '$lib/stores/navbar'

  interface Task {
    id: string
    name: string
    description: string
    type: string
    order: number
    validation: string
    initialData?: string
  }

  interface Level {
    id: string
    name: string
    description: string
    difficulty: number
    points: number
    parentId?: string | null
    tasks: Task[]
    progress: Array<{
      completed: boolean
      score: number
      attempts: number
    }>
  }

  interface MainTask {
    id: string
    name: string
    description: string
    difficulty: number
    points: number
    hasAccess: boolean
    isLocked: boolean
    buttonText?: string
    requiredScore?: number
    children: Level[]
  }

  export let data

  const { session, mainTask } = data
  let loading = false

  $: levelId = $page.params.id

  onMount(() => {
    if (mainTask) {
      // 设置导航栏数据
      const completedCount = mainTask.children.filter((level: Level) =>
        level.progress && level.progress.length > 0 && level.progress[0].completed
      ).length
      navbarStore.setNavbarData({
        completedCount,
        totalCount: mainTask.children.length,
        showLevelList: false,
        levelListHref: `/level/${levelId}`
      })
    }
  })

  const calculateProgress = (level: Level) => {
    if (!level.progress || level.progress.length === 0) return 0
    return level.progress[0].completed ? 100 : 0
  }

  const getCompletedCount = () => {
    if (!mainTask) return 0
    return mainTask.children.filter(level =>
      level.progress && level.progress.length > 0 && level.progress[0].completed
    ).length
  }

  const getEarnedPoints = () => {
    if (!mainTask) return 0
    return mainTask.children
      .filter(level => level.progress && level.progress.length > 0 && level.progress[0].completed)
      .reduce((sum, level) => sum + level.points, 0)
  }

  // 滚动处理已移至全局布局，这里不再需要
</script>

<svelte:head>
  <title>{mainTask ? mainTask.name : '关卡'} - Excel学习平台</title>
  <meta name="description" content={mainTask ? mainTask.description : '学习关卡页面'} />
</svelte:head>

{#if loading}
  <div class="min-h-screen flex items-center justify-center">
    <div class="text-lg">加载中...</div>
  </div>
{:else if !session || !mainTask}
  <!-- 会被重定向到登录页面或仪表板 -->
{:else}
  <div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50" style="padding-top: 3rem;">
    <div class="max-w-7xl mx-auto py-8 sm:px-6 lg:px-8">
      <!-- 主任务信息 -->
      <div class="bg-white shadow-xl rounded-2xl p-8 mb-8 border border-gray-100">
        <!-- 头部区域 -->
        <div class="flex items-start justify-between mb-6">
          <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg">
              <span class="text-white text-2xl">🗺️</span>
            </div>
            <div>
              <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
                {mainTask.name}
              </h1>
              <p class="text-gray-600 text-lg leading-relaxed max-w-2xl">
                {mainTask.description}
              </p>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <!-- 难度等级 -->
          <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-4 border border-yellow-200">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700">难度等级</span>
              <span class="text-yellow-600">⭐</span>
            </div>
            <div class="flex items-center space-x-1">
              {#each Array(5) as _, i}
                <span
                  class="text-lg {i < mainTask.difficulty ? 'text-yellow-500' : 'text-gray-300'}"
                >
                  ★
                </span>
              {/each}
            </div>
          </div>

          <!-- 总经验值 -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700">总经验值</span>
              <span class="text-blue-600">💎</span>
            </div>
            <div class="text-2xl font-bold text-blue-700">{mainTask.points}</div>
          </div>

          <!-- 已获经验值 -->
          <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700">已获经验值</span>
              <span class="text-green-600">🏆</span>
            </div>
            <div class="text-2xl font-bold text-green-700">{getEarnedPoints()}</div>
          </div>

          <!-- 关卡进度 -->
          <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-200">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700">关卡进度</span>
              <span class="text-purple-600">🎯</span>
            </div>
            <div class="text-2xl font-bold text-purple-700">
              {getCompletedCount()}/{mainTask.children.length}
            </div>
          </div>
        </div>
      </div>

      <!-- 子任务列表 -->
      <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">
          🧩 关卡列表
        </h2>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {#each mainTask.children as level}
          {@const progress = calculateProgress(level)}
          {@const isCompleted = progress === 100}
          {@const userProgress = level.progress && level.progress.length > 0 ? level.progress[0] : null}
          {@const isAdvancedLevel = mainTask.name === '进阶操作' || mainTask.name === '实用技巧'}
          {@const isLocked = isAdvancedLevel && mainTask.isLocked}

          <div
            class="bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 flex flex-col h-full relative {isCompleted ? 'ring-2 ring-green-200 bg-gradient-to-br from-green-50 to-white' : isLocked ? 'opacity-75 cursor-not-allowed' : 'hover:border-blue-200'}"
          >
            <!-- 锁定/解锁图标 -->
            {#if isAdvancedLevel}
              <div class="absolute top-4 right-4 z-10">
                {#if isLocked}
                  <div class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center shadow-lg">
                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                {:else}
                  <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                    </svg>
                  </div>
                {/if}
              </div>
            {/if}

            <!-- 卡片顶部装饰条 -->
            <div class="h-1 rounded-full mb-6 {isCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' : isLocked ? 'bg-gradient-to-r from-red-400 to-red-500' : 'bg-gradient-to-r from-blue-500 to-indigo-500'}"></div>

            <!-- 头部区域 -->
            <div class="flex items-start justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 rounded-xl flex items-center justify-center shadow-lg {isCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' : 'bg-gradient-to-r from-blue-500 to-indigo-500'}">
                  <span class="text-white text-xl">
                    {isCompleted ? '✅' : '🧩'}
                  </span>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-gray-900 mb-1">
                    {level.name}
                  </h3>
                  <!-- 为已完成状态预留空间，保持高度一致 -->
                  <div class="h-6 flex items-center">
                    {#if isCompleted}
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        已完成
                      </span>
                    {/if}
                  </div>
                </div>
              </div>
            </div>

            <div class="flex-1">
              <p class="text-gray-600 mb-6 leading-relaxed">
                {level.description}
              </p>

              <!-- 统计信息网格 -->
              <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="bg-gray-50 rounded-xl p-3 text-center">
                  <div class="flex items-center justify-center space-x-1 mb-1">
                    {#each Array(5) as _, i}
                      <span
                        class="text-sm {i < level.difficulty ? 'text-yellow-500' : 'text-gray-300'}"
                      >
                        ★
                      </span>
                    {/each}
                  </div>
                  <div class="text-xs text-gray-500">难度等级</div>
                </div>

                <div class="bg-gray-50 rounded-xl p-3 text-center">
                  <div class="text-lg font-bold text-blue-600">{level.points}</div>
                  <div class="text-xs text-gray-500">经验值</div>
                </div>
              </div>

              <!-- 进度条 -->
              <div class="mb-6">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-700">学习进度</span>
                  <span class="text-sm font-bold text-gray-900">{progress}%</span>
                </div>
                <div class="bg-gray-200 rounded-full h-3 overflow-hidden">
                  <div
                    class="h-3 rounded-full transition-all duration-500 {isCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' : 'bg-gradient-to-r from-blue-500 to-indigo-500'}"
                    style="width: {progress}%"
                  ></div>
                </div>
                {#if userProgress}
                  <div class="mt-2 text-center">
                    <span class="text-xs text-gray-500">
                      已获得 <span class="font-medium text-green-600">{userProgress.score}</span> 经验值
                    </span>
                  </div>
                {/if}
              </div>
            </div>

            <!-- 行动按钮 - 固定在底部 -->
            <div class="mt-auto">
              {#if isLocked}
                <!-- 根据用户类型和经验值显示不同的按钮 -->
                {#if session.user.userType === 'beta'}
                  <!-- Beta用户可以访问所有专属区域 -->
                  <a
                    href="/task/{level.tasks[0]?.id || level.id}"
                    class="block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold bg-gradient-to-r from-yellow-400 to-amber-400 text-amber-900 hover:from-yellow-500 hover:to-amber-500 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    <span class="flex items-center justify-center space-x-2">
                      <span>专属区域，已对您开放 →</span>
                    </span>
                  </a>
                {:else if session.user.userType === 'friend'}
                  <!-- Friend用户根据关卡和经验值判断 -->
                  {#if level.name === '进阶操作'}
                    <a
                      href="/task/{level.tasks[0]?.id || level.id}"
                      class="block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold bg-gradient-to-r from-yellow-400 to-amber-400 text-amber-900 hover:from-yellow-500 hover:to-amber-500 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      <span class="flex items-center justify-center space-x-2">
                        <span>专属区域，已对您开放 →</span>
                      </span>
                    </a>
                  {:else if level.name === '实用技巧' && session.user.score >= 600}
                    <a
                      href="/task/{level.tasks[0]?.id || level.id}"
                      class="block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold bg-gradient-to-r from-yellow-400 to-amber-400 text-amber-900 hover:from-yellow-500 hover:to-amber-500 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      <span class="flex items-center justify-center space-x-2">
                        <span>专属区域，已对您开放 →</span>
                      </span>
                    </a>
                  {:else if level.name === '实用技巧'}
                    <div class="block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold bg-gray-300 text-gray-500 cursor-not-allowed">
                      <span class="flex items-center justify-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <span>专属区域，经验值600以上开放</span>
                      </span>
                    </div>
                  {:else}
                    <div class="block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold bg-gray-300 text-gray-500 cursor-not-allowed">
                      <span class="flex items-center justify-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <span>需要邀请码解锁</span>
                      </span>
                    </div>
                  {/if}
                {:else}
                  <!-- 普通用户根据经验值判断 -->
                  {#if level.name === '进阶操作' && session.user.score >= 500}
                    <a
                      href="/task/{level.tasks[0]?.id || level.id}"
                      class="block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold bg-gradient-to-r from-yellow-400 to-amber-400 text-amber-900 hover:from-yellow-500 hover:to-amber-500 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      <span class="flex items-center justify-center space-x-2">
                        <span>专属区域，已对您开放 →</span>
                      </span>
                    </a>
                  {:else if level.name === '进阶操作'}
                    <div class="block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold bg-gray-300 text-gray-500 cursor-not-allowed">
                      <span class="flex items-center justify-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <span>专属区域，经验值500以上开放</span>
                      </span>
                    </div>
                  {:else if level.name === '实用技巧' && session.user.score >= 600}
                    <a
                      href="/task/{level.tasks[0]?.id || level.id}"
                      class="block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold bg-gradient-to-r from-yellow-400 to-amber-400 text-amber-900 hover:from-yellow-500 hover:to-amber-500 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      <span class="flex items-center justify-center space-x-2">
                        <span>专属区域，已对您开放 →</span>
                      </span>
                    </a>
                  {:else if level.name === '实用技巧'}
                    <div class="block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold bg-gray-300 text-gray-500 cursor-not-allowed">
                      <span class="flex items-center justify-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <span>专属区域，经验值600以上开放</span>
                      </span>
                    </div>
                  {:else}
                    <div class="block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold bg-gray-300 text-gray-500 cursor-not-allowed">
                      <span class="flex items-center justify-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <span>需要邀请码解锁</span>
                      </span>
                    </div>
                  {/if}
                {/if}
              {:else}
                <a
                  href="/task/{level.tasks[0]?.id || level.id}"
                  class="block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl {isCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:from-green-600 hover:to-emerald-600' : 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:from-blue-600 hover:to-indigo-600'}"
                >
                  <span class="flex items-center justify-center space-x-2">
                    <span>{isCompleted ? '🔄 再次挑战' : '🚀 开始挑战'}</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                </a>
              {/if}
            </div>
          </div>
        {/each}
      </div>
    </div>
  </div>
{/if}
