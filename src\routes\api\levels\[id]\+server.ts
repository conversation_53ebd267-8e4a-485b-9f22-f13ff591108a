import { json } from '@sveltejs/kit'
import type { Request<PERSON><PERSON><PERSON> } from './$types'
import { prisma } from '$lib/db'
import { log } from '$lib/logger'
import { hasAdvancedAccess } from '$lib/invite-codes'
import { getSession } from '$lib/session'

export const GET: RequestHandler = async ({ params, cookies }) => {
  try {
    const session = await getSession(cookies)

    if (!session?.user) {
      return json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const level = await prisma.level.findUnique({
      where: {
        id: params.id
      },
      include: {
        tasks: {
          orderBy: {
            order: 'asc'
          }
        },
        children: {
          orderBy: {
            order: 'asc'
          },
          include: {
            tasks: {
              orderBy: {
                order: 'asc'
              }
            },
            progress: {
              where: {
                userId: session.user.id
              }
            }
          }
        },
        progress: {
          where: {
            userId: session.user.id
          }
        },
        parent: true
      }
    })

    if (!level) {
      return json(
        { error: '关卡不存在' },
        { status: 404 }
      )
    }

    // 获取用户信息以检查权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { userType: true, score: true }
    })

    // 检查是否是受限关卡
    const parentName = level.parent?.name || level.name
    const hasAccess = hasAdvancedAccess(user?.userType || 'normal', user?.score || 0, parentName)

    if (!hasAccess) {
      const errorMessage = user?.userType === 'normal'
        ? (parentName === '进阶操作'
          ? '您需要达到500经验值才能访问此关卡'
          : '您需要达到600经验值才能访问此关卡')
        : (user?.userType === 'friend' && parentName === '实用技巧'
          ? '您需要达到600经验值才能访问此关卡'
          : '您没有权限访问此关卡，请使用邀请码注册以获得访问权限')

      return json(
        { error: errorMessage },
        { status: 403 }
      )
    }

    // 合并子级别的任务和进度到主级别
    const allTasks = level.children.flatMap(child => child.tasks)
    const allProgress = level.children.flatMap(child => child.progress)

    return json({
      ...level,
      tasks: allTasks,
      progress: [...level.progress, ...allProgress],
      hasAccess,
      isLocked: false
    })
  } catch (error) {
    log.error('获取关卡详情错误:', error)
    return json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
