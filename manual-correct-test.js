/**
 * 手动测试正确操作
 * 简化版本，用于快速验证几个关键任务
 */

import { chromium } from 'playwright';

async function quickTest() {
  console.log('🚀 快速测试正确操作');
  console.log('📋 将测试字体更改任务');

  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 登录
    console.log('\n🔐 登录...');
    await page.goto('http://localhost:5173/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', '123456');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);

    // 导航到字体更改任务
    console.log('\n🌐 导航到字体更改任务...');
    await page.goto('http://localhost:5173/task/cmdsdv4z1000hu4804evogf7v');
    await page.waitForTimeout(5000);

    console.log('\n📋 请执行以下操作:');
    console.log('1. 选中B1单元格');
    console.log('2. 在字体下拉框中选择"宋体"');
    console.log('3. 完成后，脚本将自动提交');

    // 等待用户操作
    console.log('\n⏸️ 等待60秒让您执行操作...');
    await page.waitForTimeout(60000);

    // 提交任务
    console.log('\n🖱️ 提交任务...');
    const submitButton = page.locator('button:has-text("🚀 提交任务")');
    await submitButton.click();
    await page.waitForTimeout(5000);

    // 检查结果
    const pageText = await page.textContent('body');
    if (pageText.includes('闯关成功') || pageText.includes('✅')) {
      console.log('✅ 任务成功完成！');
    } else if (pageText.includes('闯关失败') || pageText.includes('❌')) {
      console.log('❌ 任务失败，请检查操作是否正确');
    } else {
      console.log('❓ 结果不明确');
    }

    console.log('\n🔚 测试完成，浏览器保持打开供检查');

  } catch (error) {
    console.error('❌ 错误:', error);
  }
}

quickTest();
