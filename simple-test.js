// 简化的浏览器测试
import { chromium } from '@playwright/test'

async function simpleTest() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  })
  
  const page = await browser.newPage()
  
  try {
    console.log('🌐 开始简化测试...')
    
    // 1. 登录
    await page.goto('http://localhost:5173/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    // 2. 检查初始经验值（应该是0）
    const navText = await page.textContent('nav')
    console.log('📊 导航栏经验值:', navText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '未找到')
    
    // 3. 进入初识表格
    await page.click('text=初识表格')
    await page.waitForLoadState('networkidle')
    console.log('✅ 进入初识表格')
    
    // 4. 点击数据输入任务
    const dataInputCard = page.locator('text=数据输入').locator('..')
    const buttonText = await dataInputCard.locator('a').last().textContent()
    console.log('🎯 数据输入按钮文本:', buttonText)
    
    await dataInputCard.locator('a').last().click()
    await page.waitForLoadState('networkidle')
    console.log('✅ 进入数据输入任务')
    
    // 5. 查找并点击完成按钮
    const completeButtons = [
      'button:has-text("完成任务")',
      'button:has-text("提交答案")', 
      'button:has-text("完成")',
      'button:has-text("提交")',
      '.btn:has-text("完成")',
      '.btn:has-text("提交")'
    ]
    
    let buttonFound = false
    for (const selector of completeButtons) {
      const button = page.locator(selector).first()
      if (await button.isVisible()) {
        console.log(`✅ 找到完成按钮: ${selector}`)
        await button.click()
        buttonFound = true
        break
      }
    }
    
    if (!buttonFound) {
      console.log('❌ 未找到完成按钮，尝试查看页面内容')
      const pageContent = await page.textContent('body')
      console.log('页面内容片段:', pageContent.substring(0, 500))
    }
    
    // 等待可能的API调用
    await page.waitForTimeout(3000)
    
    // 6. 返回dashboard检查经验值
    await page.goto('http://localhost:5173/dashboard')
    await page.waitForLoadState('networkidle')
    
    const updatedNavText = await page.textContent('nav')
    const updatedScore = updatedNavText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '未找到'
    console.log('📊 更新后导航栏经验值:', updatedScore)
    
    // 7. 再次检查数据输入按钮状态
    await page.click('text=初识表格')
    await page.waitForLoadState('networkidle')
    
    const updatedDataInputCard = page.locator('text=数据输入').locator('..')
    const updatedButtonText = await updatedDataInputCard.locator('a').last().textContent()
    console.log('🎯 更新后数据输入按钮文本:', updatedButtonText)
    
    // 8. 检查数据库状态
    console.log('🔍 检查数据库状态...')
    
    console.log('🎉 测试完成！浏览器将保持打开60秒供检查...')
    await page.waitForTimeout(60000)
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    await page.screenshot({ path: 'test-error.png' })
  } finally {
    await browser.close()
  }
}

simpleTest()
