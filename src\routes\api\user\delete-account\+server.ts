import { json } from '@sveltejs/kit'
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types'
import { prisma } from '$lib/db'
import { log } from '$lib/logger'
import { getSession } from '$lib/session'

export const DELETE: RequestHandler = async ({ cookies }) => {
  try {
    const session = await getSession(cookies)

    if (!session?.user) {
      return json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const userId = session.user.id

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { 
        email: true, 
        username: true,
        userType: true
      }
    })

    if (!user) {
      return json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 在事务中删除用户及相关数据
    await prisma.$transaction(async (tx) => {
      // 删除用户的学习进度
      await tx.progress.deleteMany({
        where: { userId }
      })

      // 删除用户创建的邀请码
      await tx.inviteCode.deleteMany({
        where: { createdById: userId }
      })

      // 删除用户的验证令牌
      await tx.verificationToken.deleteMany({
        where: { 
          identifier: user.email 
        }
      })

      // 删除用户账户
      await tx.user.delete({
        where: { id: userId }
      })
    })

    // 记录账户删除日志
    log('info', `用户账户已删除: ${user.email} (${user.username})`, {
      userId,
      email: user.email,
      username: user.username,
      userType: user.userType
    })

    return json({ success: true })
  } catch (error) {
    console.error('删除账户错误:', error)
    log('error', '删除账户失败', { error: error.message })
    return json(
      { error: '删除账户失败，请稍后重试' },
      { status: 500 }
    )
  }
}
