import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function testPassword() {
  try {
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (user) {
      console.log('User found:')
      console.log('Email:', user.email)
      console.log('Stored password hash:', user.password)
      
      const testPassword = '123456'
      const isValid = await bcrypt.compare(testPassword, user.password)
      console.log('Password test result:', isValid)
      
      // 也测试一下生成新的hash
      const newHash = await bcrypt.hash(testPassword, 12)
      console.log('New hash for comparison:', newHash)
      const isNewHashValid = await bcrypt.compare(testPassword, newHash)
      console.log('New hash test result:', isNewHashValid)
    } else {
      console.log('User not found')
    }
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testPassword()
