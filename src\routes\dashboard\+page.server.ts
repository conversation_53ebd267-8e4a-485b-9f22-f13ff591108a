import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'
import { getSession } from '$lib/session'

export const load: PageServerLoad = async ({ fetch, cookies }) => {
  // 检查用户是否已登录
  const session = await getSession(cookies)

  if (!session?.user) {
    throw redirect(302, '/login')
  }

  try {
    // 获取关卡数据
    const response = await fetch('/api/levels')

    if (!response.ok) {
      throw new Error('Failed to fetch levels')
    }

    const levels = await response.json()

    return {
      session,
      levels
    }
  } catch (error) {
    console.error('Error loading dashboard data:', error)

    return {
      session,
      levels: []
    }
  }
}
