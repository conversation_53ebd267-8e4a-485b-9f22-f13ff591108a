<script lang="ts">
  // 滚动处理已移至全局布局

  export let data

  const { session, inviteCodes } = data

  // 状态管理
  let loading = false

  // 分别的错误和消息状态
  let usernameError = ''
  let usernameMessage = ''
  let passwordError = ''
  let passwordMessage = ''
  let deleteError = ''
  let deleteMessage = ''

  // 用户名修改相关状态
  let isEditingUsername = false
  let newUsername = session.user.username || ''

  // 密码修改相关状态
  let isEditingPassword = false
  let currentPassword = ''
  let newPassword = ''
  let confirmPassword = ''
  let showCurrentPassword = false
  let showNewPassword = false
  let showConfirmPassword = false

  // 账户注销相关状态
  let isEditingDelete = false
  let deleteConfirmText = ''
  let showSuccessModal = false

  // 修改用户名
  const handleUsernameUpdate = async (e: Event) => {
    e.preventDefault()
    if (!newUsername.trim()) {
      usernameError = '用户名不能为空'
      return
    }

    loading = true
    usernameError = ''
    usernameMessage = ''

    try {
      const response = await fetch('/api/user/update-username', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username: newUsername.trim() })
      })

      const data = await response.json()

      if (response.ok) {
        // 显示成功消息和关闭编辑状态
        usernameMessage = '用户名和昵称修改成功'
        isEditingUsername = false
        // 延迟清除成功消息
        setTimeout(() => {
          usernameMessage = ''
        }, 3000)
        // 刷新页面数据
        window.location.reload()
      } else {
        usernameError = data.error || '修改失败'
      }
    } catch {
      usernameError = '网络错误，请稍后重试'
    } finally {
      loading = false
    }
  }

  // 修改密码
  const handlePasswordUpdate = async (e: Event) => {
    e.preventDefault()

    if (!currentPassword || !newPassword || !confirmPassword) {
      passwordError = '所有密码字段都是必填的'
      return
    }

    if (newPassword !== confirmPassword) {
      passwordError = '新密码和确认密码不匹配'
      return
    }

    if (newPassword.length < 6) {
      passwordError = '新密码长度至少为6个字符'
      return
    }

    loading = true
    passwordError = ''
    passwordMessage = ''

    try {
      const response = await fetch('/api/user/update-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPassword,
          newPassword
        })
      })

      const data = await response.json()

      if (response.ok) {
        passwordMessage = '密码修改成功'
        isEditingPassword = false
        currentPassword = ''
        newPassword = ''
        confirmPassword = ''
        // 延迟清除成功消息
        setTimeout(() => {
          passwordMessage = ''
        }, 3000)
      } else {
        passwordError = data.error || '修改失败'
      }
    } catch {
      passwordError = '网络错误，请稍后重试'
    } finally {
      loading = false
    }
  }

  // 注销账户
  const handleDeleteAccount = async () => {
    if (deleteConfirmText !== '确认注销') {
      deleteError = '请输入"确认注销"来确认操作'
      return
    }

    loading = true
    deleteError = ''

    try {
      const response = await fetch('/api/user/delete-account', {
        method: 'DELETE'
      })

      if (response.ok) {
        // 显示自定义成功模态框
        showSuccessModal = true
        // 3秒后跳转到首页
        setTimeout(() => {
          // 使用window.location.replace确保不能通过后退按钮返回
          window.location.replace('/')
        }, 3000)
      } else {
        // 只在失败时解析JSON
        let errorMessage = '注销失败，请稍后重试'
        try {
          const data = await response.json()
          errorMessage = data.error || errorMessage
        } catch {
          // 如果JSON解析失败，使用默认错误消息
        }
        deleteError = errorMessage
      }
    } catch (error) {
      console.error('注销账户错误:', error)
      deleteError = '网络错误，请稍后重试'
    } finally {
      loading = false
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('邀请码已复制到剪贴板')
    })
  }

  // 滚动处理已移至全局布局，这里不再需要
</script>

<svelte:head>
  <title>个人中心 - Excel学习平台</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 py-8" style="padding-top: 5rem;">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">个人中心</h1>
      <p class="text-gray-600 mt-2">查看和管理您的账户信息</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- 左侧：基本信息 -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">基本信息</h2>

          <div class="space-y-4">
            <div class="flex items-center space-x-3">
              {#if session.user.userType === 'beta'}
                <div class="w-12 h-12 flex items-center justify-center">
                  <img
                    src="/icon.svg"
                    alt="VIP"
                    class="w-10 h-10"
                  />
                </div>
              {:else}
                <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full flex items-center justify-center">
                  <span class="text-white text-lg font-bold">
                    {session.user.username?.charAt(0).toUpperCase()}
                  </span>
                </div>
              {/if}
              <div>
                <p class="font-medium text-gray-900">{session.user.username}</p>
                <p class="text-sm text-gray-500">{session.user.email}</p>
              </div>
            </div>

            <div class="pt-4 border-t border-gray-200">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm text-gray-600">经验值</span>
                <span class="text-sm font-medium text-blue-600">{session.user.score} EXP</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">账户类型</span>
                <span class="text-sm font-medium {
                  session.user.userType === 'beta' ? 'text-yellow-600' :
                  session.user.userType === 'friend' ? 'text-purple-600' : 'text-gray-600'
                }">
                  {session.user.userType === 'beta' ? '内测用户' :
                   session.user.userType === 'friend' ? '好友用户' : '普通用户'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：设置选项 -->
      <div class="lg:col-span-2">
        <div class="space-y-6">
          <!-- 修改用户名 -->
          <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">修改用户名/昵称</h3>
              {#if !isEditingUsername}
                <button
                  on:click={() => isEditingUsername = true}
                  class="text-green-600 hover:text-green-700 text-sm font-medium"
                >
                  修改
                </button>
              {/if}
            </div>

            <!-- 用户名成功提示 -->
            {#if usernameMessage}
              <div class="p-3 bg-green-50 border border-green-200 rounded-lg mb-4">
                <p class="text-green-700 text-sm">{usernameMessage}</p>
              </div>
            {/if}

            {#if isEditingUsername}
              <form on:submit={handleUsernameUpdate} class="space-y-4">
                <div>
                  <label for="username" class="block text-sm font-medium text-gray-700 mb-1">
                    新用户名/昵称
                  </label>
                  <input
                    type="text"
                    id="username"
                    bind:value={newUsername}
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    placeholder="请输入新的用户名"
                    required
                  />
                </div>

                {#if usernameError}
                  <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p class="text-red-700 text-sm">{usernameError}</p>
                  </div>
                {/if}

                <div class="flex space-x-3">
                  <button
                    type="submit"
                    disabled={loading}
                    class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? '保存中...' : '保存'}
                  </button>
                  <button
                    type="button"
                    on:click={() => {
                      isEditingUsername = false
                      newUsername = session.user.username || ''
                      usernameError = ''
                    }}
                    class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"
                  >
                    取消
                  </button>
                </div>
              </form>
            {:else}
              <div class="text-sm text-gray-600">
                当前用户名：<span class="font-medium">{session.user.username}</span>
              </div>
            {/if}
          </div>

          <!-- 修改密码 -->
          <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">修改密码</h3>
              {#if !isEditingPassword}
                <button
                  on:click={() => isEditingPassword = true}
                  class="text-green-600 hover:text-green-700 text-sm font-medium"
                >
                  修改
                </button>
              {/if}
            </div>

            <!-- 密码成功提示 -->
            {#if passwordMessage}
              <div class="p-3 bg-green-50 border border-green-200 rounded-lg mb-4">
                <p class="text-green-700 text-sm">{passwordMessage}</p>
              </div>
            {/if}

            {#if isEditingPassword}
              <form on:submit={handlePasswordUpdate} class="space-y-4">
                <div>
                  <label for="currentPassword" class="block text-sm font-medium text-gray-700 mb-1">
                    当前密码
                  </label>
                  <div class="relative">
                    <input
                      type={showCurrentPassword ? "text" : "password"}
                      id="currentPassword"
                      bind:value={currentPassword}
                      class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      placeholder="请输入当前密码"
                      required
                    />
                    <button
                      type="button"
                      class="absolute inset-y-0 right-0 pr-3 flex items-center"
                      on:click={() => showCurrentPassword = !showCurrentPassword}
                    >
                      {#if showCurrentPassword}
                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 11-4.243-4.243m4.243 4.243L9.88 9.88" />
                        </svg>
                      {:else}
                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      {/if}
                    </button>
                  </div>
                </div>

                <div>
                  <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-1">
                    新密码
                  </label>
                  <div class="relative">
                    <input
                      type={showNewPassword ? "text" : "password"}
                      id="newPassword"
                      bind:value={newPassword}
                      class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      placeholder="请输入新密码（至少6个字符）"
                      required
                    />
                    <button
                      type="button"
                      class="absolute inset-y-0 right-0 pr-3 flex items-center"
                      on:click={() => showNewPassword = !showNewPassword}
                    >
                      {#if showNewPassword}
                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 11-4.243-4.243m4.243 4.243L9.88 9.88" />
                        </svg>
                      {:else}
                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      {/if}
                    </button>
                  </div>
                </div>

                <div>
                  <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">
                    确认新密码
                  </label>
                  <div class="relative">
                    <input
                      type={showConfirmPassword ? "text" : "password"}
                      id="confirmPassword"
                      bind:value={confirmPassword}
                      class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      placeholder="请再次输入新密码"
                      required
                    />
                    <button
                      type="button"
                      class="absolute inset-y-0 right-0 pr-3 flex items-center"
                      on:click={() => showConfirmPassword = !showConfirmPassword}
                    >
                      {#if showConfirmPassword}
                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 11-4.243-4.243m4.243 4.243L9.88 9.88" />
                        </svg>
                      {:else}
                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      {/if}
                    </button>
                  </div>
                </div>

                {#if passwordError}
                  <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p class="text-red-700 text-sm">{passwordError}</p>
                  </div>
                {/if}

                <div class="flex space-x-3">
                  <button
                    type="submit"
                    disabled={loading}
                    class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? '保存中...' : '保存'}
                  </button>
                  <button
                    type="button"
                    on:click={() => {
                      isEditingPassword = false
                      currentPassword = ''
                      newPassword = ''
                      confirmPassword = ''
                      passwordError = ''
                    }}
                    class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"
                  >
                    取消
                  </button>
                </div>
              </form>
            {:else}
              <div class="text-sm text-gray-600">
                点击"修改"按钮来更改您的密码
              </div>
            {/if}
          </div>

          <!-- 注销账户 -->
          <div class="bg-white rounded-xl shadow-sm border border-red-200 p-6">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-lg font-semibold text-red-900">注销账户</h3>
                <p class="text-sm text-red-600 mt-1">永久删除您的账户和所有数据</p>
              </div>
              {#if !isEditingDelete}
                <button
                  on:click={() => isEditingDelete = true}
                  class="text-red-600 hover:text-red-700 text-sm font-medium"
                >
                  注销
                </button>
              {/if}
            </div>

            {#if isEditingDelete}
              <div class="space-y-4">
                <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p class="text-red-800 text-sm font-medium mb-2">⚠️ 警告：此操作不可撤销</p>
                  <p class="text-red-700 text-sm">
                    注销账户将永久删除您的所有数据，包括学习进度、经验值等。请确认您真的要继续。
                  </p>
                </div>

                <div>
                  <label for="deleteConfirm" class="block text-sm font-medium text-gray-700 mb-1">
                    请输入"确认注销"来确认操作
                  </label>
                  <input
                    type="text"
                    id="deleteConfirm"
                    bind:value={deleteConfirmText}
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="确认注销"
                  />
                </div>

                {#if deleteError}
                  <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p class="text-red-700 text-sm">{deleteError}</p>
                  </div>
                {/if}

                <div class="flex space-x-3">
                  <button
                    type="button"
                    on:click={handleDeleteAccount}
                    disabled={loading}
                    class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? '注销中...' : '确认注销'}
                  </button>
                  <button
                    type="button"
                    on:click={() => {
                      isEditingDelete = false
                      deleteConfirmText = ''
                      deleteError = ''
                    }}
                    class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"
                  >
                    取消
                  </button>
                </div>
              </div>
            {:else}
              <div class="text-sm text-gray-600">
                此操作将永久删除您的账户和所有相关数据
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<!-- 成功模态框 -->
{#if showSuccessModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-xl p-8 max-w-md mx-4 text-center">
      <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">账户注销成功</h3>
      <p class="text-gray-600 mb-4">您的账户已成功注销，感谢您的使用。</p>
      <p class="text-sm text-gray-500">3秒后将自动跳转到首页...</p>
    </div>
  </div>
{/if}
