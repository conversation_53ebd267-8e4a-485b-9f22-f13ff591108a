import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function checkUser() {
  try {
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (user) {
      console.log('User found:')
      console.log('Email:', user.email)
      console.log('Email verified:', user.emailVerified)
      console.log('Username:', user.username)
    } else {
      console.log('User not found')
    }
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkUser()
