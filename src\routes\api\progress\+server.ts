import { json } from '@sveltejs/kit'
import type { RequestHand<PERSON> } from './$types'
import { prisma } from '$lib/db'
import { log } from '$lib/logger'
import { allocateFriendCodes } from '$lib/invite-codes'
import { getSession } from '$lib/session'

export const GET: RequestHandler = async ({ cookies }) => {
  try {
    const session = await getSession(cookies)
    
    if (!session?.user) {
      return json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const progress = await prisma.userProgress.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        level: true
      },
      orderBy: {
        level: {
          order: 'asc'
        }
      }
    })

    return json(progress)
  } catch (error) {
    log.error('获取进度错误:', error)
    return json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const session = await getSession(cookies)
    
    if (!session?.user) {
      return json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const { taskId, levelId, completed, score } = await request.json()

    if (!levelId) {
      return json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    // 检查是否已存在进度记录
    const existingProgress = await prisma.userProgress.findUnique({
      where: {
        userId_levelId: {
          userId: session.user.id,
          levelId: levelId
        }
      }
    })

    if (existingProgress) {
      // 更新进度（允许重复挑战）
      const updatedProgress = await prisma.userProgress.update({
        where: {
          id: existingProgress.id
        },
        data: {
          completed: completed || false,
          score: score || 0,
          attempts: existingProgress.attempts + 1,
          completedAt: completed ? new Date() : null
        }
      })

      // 如果任务完成且是首次完成，更新用户总积分
      if (completed && !existingProgress.completed) {
        // 获取用户当前信息
        const currentUser = await prisma.user.findUnique({
          where: { id: session.user.id },
          select: { score: true, userType: true }
        })

        const oldScore = currentUser?.score || 0
        const newScore = oldScore + (score || 0)

        // 更新用户积分
        await prisma.user.update({
          where: {
            id: session.user.id
          },
          data: {
            score: {
              increment: score || 0
            }
          }
        })

        log.debug(`用户 ${session.user.id} 首次完成关卡 ${levelId}，增加 ${score || 0} 积分`)

        // 检查是否需要分配好友邀请码（普通用户和邀请用户都可以获得）
        if ((currentUser?.userType === 'normal' || currentUser?.userType === 'friend') && oldScore < 50 && newScore >= 50) {
          try {
            await allocateFriendCodes(session.user.id, 3)
            log.debug(`用户 ${session.user.id} (${currentUser.userType}) 达到50经验值，自动分配了3个好友邀请码`)
          } catch (error) {
            log.error('自动分配好友邀请码失败:', error)
            // 不影响主流程，只记录错误
          }
        }
      } else if (completed && existingProgress.completed) {
        log.debug(`用户 ${session.user.id} 重复完成关卡 ${levelId}，不增加积分`)
      }

      return json(updatedProgress)
    } else {
      // 创建新的进度记录
      const newProgress = await prisma.userProgress.create({
        data: {
          userId: session.user.id,
          levelId: levelId,
          completed: completed || false,
          score: score || 0,
          attempts: 1,
          completedAt: completed ? new Date() : null
        }
      })

      // 如果任务完成，更新用户总积分
      if (completed) {
        // 获取用户当前信息
        const currentUser = await prisma.user.findUnique({
          where: { id: session.user.id },
          select: { score: true, userType: true }
        })

        const oldScore = currentUser?.score || 0
        const newScore = oldScore + (score || 0)

        // 更新用户积分
        await prisma.user.update({
          where: {
            id: session.user.id
          },
          data: {
            score: {
              increment: score || 0
            }
          }
        })

        log.debug(`用户 ${session.user.id} 首次完成关卡 ${levelId}，增加 ${score || 0} 积分`)

        // 检查是否需要分配好友邀请码（普通用户和邀请用户都可以获得）
        if ((currentUser?.userType === 'normal' || currentUser?.userType === 'friend') && oldScore < 50 && newScore >= 50) {
          try {
            await allocateFriendCodes(session.user.id, 3)
            log.debug(`用户 ${session.user.id} (${currentUser.userType}) 达到50经验值，自动分配了3个好友邀请码`)
          } catch (error) {
            log.error('自动分配好友邀请码失败:', error)
            // 不影响主流程，只记录错误
          }
        }
      }

      return json(newProgress)
    }
  } catch (error) {
    log.error('更新进度错误:', error)
    return json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
