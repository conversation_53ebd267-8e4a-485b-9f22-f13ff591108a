import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function getTaskIds() {
  try {
    const tasks = await prisma.task.findMany({
      include: {
        level: true
      },
      orderBy: [
        { level: { order: 'asc' } },
        { order: 'asc' }
      ]
    });

    console.log('所有任务ID和名称:');
    console.log('='.repeat(60));
    
    tasks.forEach((task, index) => {
      console.log(`${index + 1}. ${task.name} (${task.level.name})`);
      console.log(`   ID: ${task.id}`);
      console.log('');
    });
    
    // 输出关键任务的ID
    const keyTaskNames = ['字体颜色设置', '字体更改', '数字格式', '简单条件格式'];
    console.log('\n关键任务ID:');
    console.log('='.repeat(40));
    
    keyTaskNames.forEach(name => {
      const task = tasks.find(t => t.name === name);
      if (task) {
        console.log(`${name}: ${task.id}`);
      } else {
        console.log(`${name}: 未找到`);
      }
    });
    
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

getTaskIds();
