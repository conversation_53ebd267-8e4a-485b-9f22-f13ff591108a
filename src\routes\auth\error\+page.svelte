<script lang="ts">
  import { page } from '$app/stores'
  
  $: error = $page.url.searchParams.get('error')
  
  const getErrorMessage = (error: string | null) => {
    switch (error) {
      case 'Configuration':
        return '服务器配置错误，请联系管理员'
      case 'AccessDenied':
        return '访问被拒绝'
      case 'Verification':
        return '验证失败'
      default:
        return '发生未知错误'
    }
  }
</script>

<svelte:head>
  <title>认证错误 - Excel学习平台</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div class="text-center">
      <div class="mx-auto h-12 w-12 text-red-600">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
      </div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        认证错误
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        {getErrorMessage(error)}
      </p>
    </div>
    
    <div class="mt-8 space-y-4">
      <div class="text-center">
        <a href="/auth/signin" class="font-medium text-blue-600 hover:text-blue-500">
          返回登录页面
        </a>
      </div>
      <div class="text-center">
        <a href="/" class="font-medium text-gray-600 hover:text-gray-500">
          返回首页
        </a>
      </div>
    </div>
  </div>
</div>
