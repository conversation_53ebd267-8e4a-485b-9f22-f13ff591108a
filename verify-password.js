import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function verifyPassword() {
  try {
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (user) {
      console.log('User found:')
      console.log('Email:', user.email)
      console.log('Username:', user.username)
      console.log('Password hash:', user.password)
      
      // 测试密码验证
      const testPassword = 'password123'
      const isValid = await bcrypt.compare(testPassword, user.password)
      console.log('Password "password123" is valid:', isValid)
      
      // 生成新的密码哈希用于比较
      const newHash = await bcrypt.hash(testPassword, 10)
      console.log('New hash for comparison:', newHash)
      
      // 测试新哈希
      const isNewHashValid = await bcrypt.compare(testPassword, newHash)
      console.log('New hash is valid:', isNewHashValid)
      
    } else {
      console.log('User not found')
    }
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

verifyPassword()
