// 验证测试
import { chromium } from '@playwright/test'

async function validationTest() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 3000
  })
  
  const page = await browser.newPage()
  
  // 监听控制台消息
  page.on('console', msg => {
    console.log(`🖥️ 浏览器控制台 [${msg.type()}]:`, msg.text())
  })
  
  try {
    console.log('🔍 开始验证测试...')
    
    // 1. 登录
    await page.goto('http://localhost:5173/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    // 2. 直接访问数据输入任务页面
    const taskId = 'cmdsdv4yp0009u480e9dd3pw8'
    await page.goto(`http://localhost:5173/task/${taskId}`)
    await page.waitForLoadState('networkidle')
    console.log('✅ 进入数据输入任务页面')
    
    // 3. 等待Univer加载完成
    await page.waitForTimeout(5000)
    
    // 4. 在A1单元格输入"Hello Excel"
    console.log('📝 在A1单元格输入"Hello Excel"...')
    
    // 尝试多种方式点击A1单元格
    const cellSelectors = [
      '.univer-cell[data-row="0"][data-col="0"]',
      '.univer-cell:first-child',
      '.luckysheet-cell-main .luckysheet-cell',
      '.univer-sheet-container .univer-cell',
      'canvas'
    ]
    
    let cellClicked = false
    for (const selector of cellSelectors) {
      try {
        const cell = page.locator(selector).first()
        if (await cell.isVisible()) {
          console.log(`🎯 尝试点击单元格: ${selector}`)
          await cell.click()
          cellClicked = true
          break
        }
      } catch (e) {
        // 继续尝试下一个选择器
      }
    }
    
    if (!cellClicked) {
      // 如果找不到单元格，尝试点击canvas区域
      console.log('🎯 尝试点击canvas区域...')
      const canvas = page.locator('canvas').first()
      if (await canvas.isVisible()) {
        await canvas.click({ position: { x: 50, y: 50 } })
        cellClicked = true
      }
    }
    
    if (cellClicked) {
      console.log('✅ 成功点击单元格')
      
      // 输入文本
      await page.keyboard.type('Hello Excel')
      await page.keyboard.press('Enter')
      console.log('✅ 输入文本完成')
      
      // 等待一下
      await page.waitForTimeout(2000)
    } else {
      console.log('❌ 无法找到可点击的单元格')
    }
    
    // 5. 点击提交按钮
    const submitButton = page.locator('button:has-text("提交任务")').first()
    if (await submitButton.isVisible()) {
      console.log('✅ 找到提交按钮，点击中...')
      await submitButton.click()
      console.log('✅ 点击提交按钮')
      
      // 等待验证完成
      await page.waitForTimeout(8000)
      
    } else {
      console.log('❌ 未找到提交按钮')
    }
    
    console.log('🎉 验证测试完成！浏览器将保持打开60秒供检查...')
    await page.waitForTimeout(60000)
    
  } catch (error) {
    console.error('❌ 验证测试失败:', error)
    await page.screenshot({ path: 'validation-test-error.png' })
  } finally {
    await browser.close()
  }
}

validationTest()
