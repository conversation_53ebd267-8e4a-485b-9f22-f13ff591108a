// 完整的最终测试
import { chromium } from '@playwright/test'

async function finalCompleteTest() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  })
  
  const page = await browser.newPage()
  
  try {
    console.log('🎯 开始完整的最终测试...')
    
    // 1. 登录
    await page.goto('http://localhost:5173/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    // 2. 检查初始经验值
    const initialNavText = await page.textContent('nav')
    const initialScore = initialNavText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '0'
    console.log('📊 初始经验值:', initialScore)
    
    // 3. 进入初识表格关卡
    await page.click('text=初识表格')
    await page.waitForLoadState('networkidle')
    console.log('✅ 进入初识表格关卡')
    
    // 4. 检查数据输入按钮状态
    const dataInputCard = page.locator('h3:has-text("数据输入")').locator('..')
    const dataInputButton = dataInputCard.locator('a[href^="/task/"]')
    const initialButtonText = await dataInputButton.textContent()
    console.log('🎯 初始按钮状态:', initialButtonText)
    
    // 5. 点击数据输入任务
    await dataInputButton.click()
    await page.waitForLoadState('networkidle')
    console.log('✅ 进入数据输入任务页面')
    
    // 6. 等待Univer加载完成
    await page.waitForTimeout(5000)
    
    // 7. 在A1单元格输入"Hello Excel"
    console.log('📝 在A1单元格输入"Hello Excel"...')
    const canvas = page.locator('canvas').first()
    await canvas.click({ position: { x: 50, y: 50 } })
    await page.keyboard.type('Hello Excel')
    await page.keyboard.press('Enter')
    console.log('✅ 输入文本完成')
    
    // 8. 等待一下
    await page.waitForTimeout(2000)
    
    // 9. 点击提交按钮
    const submitButton = page.locator('button:has-text("提交任务")').first()
    await submitButton.click()
    console.log('✅ 点击提交按钮')
    
    // 10. 等待验证和API调用完成
    await page.waitForTimeout(5000)
    
    // 11. 等待自动跳转到关卡列表（3秒后）
    console.log('⏳ 等待自动跳转...')
    await page.waitForTimeout(4000)
    
    // 12. 检查是否跳转到了关卡页面
    const currentUrl = page.url()
    console.log('📍 当前页面URL:', currentUrl)
    
    if (currentUrl.includes('/level/')) {
      console.log('✅ 成功跳转到关卡页面')
      
      // 13. 检查按钮状态是否变为"再次挑战"
      const updatedDataInputCard = page.locator('h3:has-text("数据输入")').locator('..')
      const updatedDataInputButton = updatedDataInputCard.locator('a[href^="/task/"]')
      const updatedButtonText = await updatedDataInputButton.textContent()
      console.log('🎯 更新后按钮状态:', updatedButtonText)
      
      if (updatedButtonText && updatedButtonText.includes('再次挑战')) {
        console.log('🎉 按钮状态成功更新为"再次挑战"！')
      } else {
        console.log('⚠️ 按钮状态未更新')
      }
    }
    
    // 14. 返回dashboard检查经验值
    await page.goto('http://localhost:5173/dashboard')
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)
    
    const updatedNavText = await page.textContent('nav')
    const updatedScore = updatedNavText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '0'
    console.log('📊 更新后经验值:', updatedScore)
    
    if (parseInt(updatedScore) > parseInt(initialScore)) {
      console.log('🎉 经验值成功更新！')
      console.log(`📈 经验值从 ${initialScore} 增加到 ${updatedScore}`)
    } else {
      console.log('❌ 经验值未更新')
    }
    
    // 15. 检查dashboard页面的经验值显示
    const dashboardScore = await page.locator('text=/总经验值|经验值/').locator('..').textContent()
    console.log('📊 Dashboard经验值显示:', dashboardScore)
    
    console.log('🎉 完整测试完成！')
    console.log('📋 测试结果总结:')
    console.log(`  - 初始经验值: ${initialScore}`)
    console.log(`  - 最终经验值: ${updatedScore}`)
    console.log(`  - 初始按钮状态: ${initialButtonText}`)
    console.log(`  - 最终按钮状态: ${updatedButtonText || '未检查'}`)
    
    console.log('浏览器将保持打开30秒供最终检查...')
    await page.waitForTimeout(30000)
    
  } catch (error) {
    console.error('❌ 完整测试失败:', error)
    await page.screenshot({ path: 'final-complete-test-error.png' })
  } finally {
    await browser.close()
  }
}

finalCompleteTest()
