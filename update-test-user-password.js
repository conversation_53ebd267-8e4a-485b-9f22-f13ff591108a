import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function updateTestUserPassword() {
  try {
    const password = 'password123'
    const hashedPassword = await bcrypt.hash(password, 10)
    
    console.log('Updating test user password...')
    console.log('New hash:', hashedPassword)
    
    const updatedUser = await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: { password: hashedPassword }
    })
    
    console.log('Password updated for user:', updatedUser.email)
    
    // 验证更新是否成功
    const isValid = await bcrypt.compare(password, hashedPassword)
    console.log('Password verification:', isValid)
    
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updateTestUserPassword()
