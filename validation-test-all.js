import { chromium } from 'playwright';

// 所有任务的ID列表（从数据库中获取）
const taskIds = [
  // 初识表格 (7个任务)
  'cmdsdv4yp0009u480e9dd3pw8', // 数据输入
  'cmdsdv4yp000au480e9dd3pw9', // 粗体和斜体样式
  'cmdsdv4yp000bu480e9dd3pwa', // 字体更改
  'cmdsdv4yp000cu480e9dd3pwb', // 字体颜色设置
  'cmdsdv4yp000du480e9dd3pwc', // 数字格式
  'cmdsdv4yp000eu480e9dd3pwd', // 单元格对齐方式
  'cmdsdv4yp000fu480e9dd3pwe', // 边框设置
  
  // 基本公式 (4个任务)
  'cmdsdv4yp000gu480e9dd3pwf', // 数字计算
  'cmdsdv4yp000hu480e9dd3pwg', // SUM函数
  'cmdsdv4yp000iu480e9dd3pwh', // AVERAGE函数
  'cmdsdv4yp000ju480e9dd3pwi', // IF函数
  
  // 常用操作 (7个任务)
  'cmdsdv4yp000ku480e9dd3pwj', // 简单筛选
  'cmdsdv4yp000lu480e9dd3pwk', // 复杂筛选
  'cmdsdv4yp000mu480e9dd3pwl', // 简单排序
  'cmdsdv4yp000nu480e9dd3pwm', // 复杂排序
  'cmdsdv4yp000ou480e9dd3pwn', // 简单数据验证
  'cmdsdv4yp000pu480e9dd3pwo', // 复杂数据验证
  'cmdsdv4yp000qu480e9dd3pwp', // 图表制作
  
  // 进阶公式 (2个任务)
  'cmdsdv4yp000ru480e9dd3pwq', // VLOOKUP函数
  'cmdsdv4yp000su480e9dd3pwr', // INDEX+MATCH组合
  
  // 进阶操作 (3个任务)
  'cmdsdv4yp000tu480e9dd3pws', // 简单条件格式
  'cmdsdv4yp000uu480e9dd3pwt', // 复杂条件格式
  'cmdsdv4yp000vu480e9dd3pwu', // 数据透视表
  
  // 实用技巧 (3个任务)
  'cmdsdv4yp000wu480e9dd3pwv', // 单元格合并
  'cmdsdv4yp000xu480e9dd3pww', // 文本换行
  'cmdsdv4yp000yu480e9dd3pwx'  // 公式填充
];

const taskNames = [
  '数据输入', '粗体和斜体样式', '字体更改', '字体颜色设置', '数字格式', '单元格对齐方式', '边框设置',
  '数字计算', 'SUM函数', 'AVERAGE函数', 'IF函数',
  '简单筛选', '复杂筛选', '简单排序', '复杂排序', '简单数据验证', '复杂数据验证', '图表制作',
  'VLOOKUP函数', 'INDEX+MATCH组合',
  '简单条件格式', '复杂条件格式', '数据透视表',
  '单元格合并', '文本换行', '公式填充'
];

async function testAllTasks() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000 // 减慢操作速度以便观察
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    console.log('🔍 开始第一轮测试 - 错误操作验证...')
    
    // 1. 登录
    await page.goto('http://localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    const results = []
    
    // 测试每个任务
    for (let i = 0; i < taskIds.length; i++) {
      const taskId = taskIds[i]
      const taskName = taskNames[i]
      
      console.log(`\n📝 测试任务 ${i + 1}/26: ${taskName}`)
      
      try {
        // 访问任务页面
        await page.goto(`http://localhost:5174/task/${taskId}`)
        await page.waitForLoadState('networkidle')
        
        // 等待Univer加载
        await page.waitForTimeout(3000)
        
        // 第一轮测试：不做任何操作，直接提交
        console.log('  🔸 不做任何操作，直接提交...')
        
        const submitButton = page.locator('button:has-text("提交任务")').first()
        if (await submitButton.isVisible()) {
          await submitButton.click()
          
          // 等待验证完成
          await page.waitForTimeout(5000)
          
          // 检查是否显示成功消息
          const successMessage = await page.locator('.bg-green-50, .text-green-600, .text-green-700').count()
          const errorMessage = await page.locator('.bg-red-50, .text-red-600, .text-red-700').count()
          
          if (successMessage > 0) {
            console.log(`  ❌ 任务 ${taskName} 错误地通过了验证（应该失败）`)
            results.push({ task: taskName, status: 'FAIL', reason: '不做操作却通过验证' })
          } else if (errorMessage > 0) {
            console.log(`  ✅ 任务 ${taskName} 正确地验证失败`)
            results.push({ task: taskName, status: 'PASS', reason: '正确拒绝空操作' })
          } else {
            console.log(`  ⚠️ 任务 ${taskName} 验证状态不明确`)
            results.push({ task: taskName, status: 'UNKNOWN', reason: '验证状态不明确' })
          }
        } else {
          console.log(`  ⚠️ 任务 ${taskName} 找不到提交按钮`)
          results.push({ task: taskName, status: 'ERROR', reason: '找不到提交按钮' })
        }
        
      } catch (error) {
        console.log(`  ❌ 任务 ${taskName} 测试出错: ${error.message}`)
        results.push({ task: taskName, status: 'ERROR', reason: error.message })
      }
    }
    
    // 输出测试结果
    console.log('\n📊 第一轮测试结果汇总:')
    console.log('='.repeat(50))
    
    const passCount = results.filter(r => r.status === 'PASS').length
    const failCount = results.filter(r => r.status === 'FAIL').length
    const errorCount = results.filter(r => r.status === 'ERROR').length
    const unknownCount = results.filter(r => r.status === 'UNKNOWN').length
    
    console.log(`✅ 正确验证失败: ${passCount}`)
    console.log(`❌ 错误通过验证: ${failCount}`)
    console.log(`⚠️ 测试错误: ${errorCount}`)
    console.log(`❓ 状态不明: ${unknownCount}`)
    
    if (failCount > 0) {
      console.log('\n需要修复的任务:')
      results.filter(r => r.status === 'FAIL').forEach(r => {
        console.log(`  - ${r.task}: ${r.reason}`)
      })
    }
    
    console.log('\n🎉 第一轮测试完成！')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await browser.close()
  }
}

testAllTasks()
