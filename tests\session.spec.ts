import { test, expect } from '@playwright/test'

test.describe('Session管理测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login')
  })

  test('登录后应该能访问dashboard', async ({ page }) => {
    // 登录
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'password123')
    await page.click('button[type="submit"]')

    // 应该重定向到dashboard
    await expect(page).toHaveURL('/dashboard')
    
    // 检查页面内容
    await expect(page.locator('h1')).toContainText('Excel学习平台')
  })

  test('登录后应该能访问level页面', async ({ page }) => {
    // 登录
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'password123')
    await page.click('button[type="submit"]')

    // 等待重定向到dashboard
    await expect(page).toHaveURL('/dashboard')
    
    // 尝试访问一个level页面
    await page.goto('/level/cmds65ao80001u4ikeoklf06h')
    
    // 页面应该加载成功，不应该是空白页
    await expect(page.locator('body')).not.toBeEmpty()
    
    // 检查是否有level相关的内容
    const pageContent = await page.textContent('body')
    expect(pageContent).toBeTruthy()
    expect(pageContent!.length).toBeGreaterThan(100) // 确保页面有实际内容
  })

  test('首页应该为已登录用户显示"继续学习"', async ({ page }) => {
    // 先登录
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'password123')
    await page.click('button[type="submit"]')

    // 等待重定向到dashboard
    await expect(page).toHaveURL('/dashboard')
    
    // 访问首页
    await page.goto('/')
    
    // 应该显示"继续学习"而不是"免费开始学习"
    await expect(page.locator('a').filter({ hasText: '继续学习' })).toBeVisible()
  })

  test('登出后应该重定向到首页', async ({ page }) => {
    // 先登录
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'password123')
    await page.click('button[type="submit"]')

    // 等待重定向到dashboard
    await expect(page).toHaveURL('/dashboard')
    
    // 点击登出按钮
    await page.click('button:has-text("登出")')
    
    // 应该重定向到首页
    await expect(page).toHaveURL('/')
    
    // 应该显示"免费开始学习"
    await expect(page.locator('a').filter({ hasText: '免费开始学习' })).toBeVisible()
  })
})
